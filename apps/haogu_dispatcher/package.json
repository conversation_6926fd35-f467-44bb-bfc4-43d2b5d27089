{"name": "haogu_dispatcher", "private": true, "scripts": {"tsc-check": "tsc --noEmit", "start": "ts-node ./main.ts"}, "devDependencies": {"ts-node": "^10.9.1", "typescript": "5.8.2"}, "dependencies": {"config": "workspace:*", "lib": "workspace:*", "model": "workspace:*", "service": "workspace:*", "axios": "^1.5.1", "kafkajs": "^2.2.4", "dayjs": "^1.11.13"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}