import { defineApp } from 'service/appkit/types'

export enum HumanTransferType {
  UnknownMessageType = 1,
  NotBindPhone = 2,
  ProblemSolving = 3,
  FailedToJoinGroup = 4,
  ProcessImage = 5,
  MessageSendFailed = 6,
  ReaskAnotherDay = 7,
  HesitatePayment = 8,
  LogOutNotify = 9,
  ExecutePostpone = 10,
  VoiceOrVideoCall = 11,
  SoftwareIssue = 12,
  RobotDetected = 13,
  PaidCourse = 14,
  ProcessVideo = 24,
  ProcessVideoFailed = 25,
  ExplicitlyPurchases = 26,
  ExecuteRefresherTraining= 27
}

/**
 * 定义 客户状态，是否完课，是否下单，是否完成作业等等
 */
export interface IChattingFlag {
  [key: string]: boolean | string | number | undefined
  is_complete_payment?:boolean
  after_bonding?:boolean
  is_send_666_follow_up_message?: boolean // 是否已经发送过回复666后的后续消息
  is_friend_accepted?: boolean
  is_finish_homework_day1?:boolean
  is_finish_homework_day2?:boolean
  is_finish_homework_day3?:boolean
  is_finish_homework_day4?:boolean
  is_finish_homework_day5?:boolean
  is_finish_homework_day6?:boolean
}

/**
 * 对通用层注入变量
 */
export const manifest = defineApp({
  projectName: 'haogu',
  agentName: '小钱老师',

  // 转人工 通知
  humanTransferMsgMap: {
    [HumanTransferType.UnknownMessageType]: '客户发了一个文件',
    [HumanTransferType.ProcessImage]: '客户发了一张【图片】',
    [HumanTransferType.ProcessVideo]: '客户发了一个【视频】',
    [HumanTransferType.ProblemSolving]: '客户遇到问题',
    [HumanTransferType.ProcessVideoFailed]: '客户发了一个【视频】，识别失败',
    [HumanTransferType.NotBindPhone]: '客户手机号绑定失败',
    [HumanTransferType.SoftwareIssue]: '客户软件或者课程链接出问题',
    [HumanTransferType.MessageSendFailed]: '消息发送失败',
    [HumanTransferType.ReaskAnotherDay]: '客户次日被主动提醒支付',
    [HumanTransferType.RobotDetected]: '客户识别到了AI',
    [HumanTransferType.HesitatePayment] : '客户支付犹豫',
    [HumanTransferType.LogOutNotify]: '客户直播掉线',
    [HumanTransferType.PaidCourse]: '[烟花]客户已支付[烟花]',
    [HumanTransferType.FailedToJoinGroup]: '客户拉群失败',
    [HumanTransferType.ExplicitlyPurchases]: '客户弹幕识别出支付意向',
    [HumanTransferType.ExecutePostpone]: '客户已延期',
    [HumanTransferType.ExecuteRefresherTraining]: '客户已复训',
    [HumanTransferType.VoiceOrVideoCall]: '客户发起语音/视频通话'
  },

  // 槽位提取
  extractUserSlots: {
    topicRecommendations: `- 基本信息：称呼、客户年龄，性别，所在地，职业
- 投资基础：资金情况（资金规模，投资占比），炒股经验，投资风格（风险偏好）
- 交易现状与问题：当前交易现状（盈亏情况），最大痛点（最困扰的问题，如选股困难，频繁亏损）,学习动机，期待目标，失败经历
- 决策影响因素：决策者角色（个人决策还是需要家庭成员朋友共同决策），决策关注点（如关注课程价值点，如价格、师资、方法可靠性、客户案例）,异议点（例如价格敏感、时间紧张、担忧自己学不会、信任感不足）`, // 当前项目主要关注的槽位

    topicRules: '' // 额外的提取规则说明
  },

  // Kafka 配置
  kafka: {
    brokers: [
      'alikafka-serverless-cn-db14e5zfm02-1000.alikafka.aliyuncs.com:9093',
      'alikafka-serverless-cn-db14e5zfm02-2000.alikafka.aliyuncs.com:9093',
      'alikafka-serverless-cn-db14e5zfm02-3000.alikafka.aliyuncs.com:9093'
    ],
    sasl: {
      username: 'alikafka_serverless-cn-db14e5zfm02',
      password: 'UgOTLdDdbyDctbSBT9TnaVb8h5Xw4Xu8',
      mechanism: 'plain'
    },
    clientId: 'haogu-kafka-client',
    groupId: 'haogu-kafka-group',
    ssl: {
      rejectUnauthorized: false
    },
    connectionTimeout: 10000,
    requestTimeout: 30000,
    topics: {
      scrmWorkToAiStaff: 'scrm_work_to_ai_staff',
      scrmMessageToAiStaff: 'scrm_message_to_ai_staff',
      srcmReadMarkToAiStaff: 'scrm_readMark_to_ai_staff',
      scrmCrmOrderPlacedToAiStaff : 'scrm_crm_order_placed_to_ai_staff'
    }
  }
})