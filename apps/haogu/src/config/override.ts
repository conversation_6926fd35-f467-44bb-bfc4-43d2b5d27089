import dayjs from 'dayjs'
import { AsyncLock } from 'model/lock/lock'
import { defineOverride } from 'service/appkit/types'
import { startTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { calTaskTime } from '../visualized_sop/visualized_sop_processor'
import { chatStateStoreClient, chatDBClient } from './instance/base_instance'
import { commonMessageSender } from './instance/send_message_instance'
import { extractUserSlots } from './instance/instance'
import { IChattingFlag, manifest } from './manifest'
import { ScrmOrderPlacedToAiStaff, ScrmWorkToAiStaff, ScrmCustomerEventToAiStaff, ScrmLinkReadMarkToAiStaff, ScrmMessageToAiStaff, ScrmReadMarkToAiStaff } from 'model/haogu/callback/type'
import logger from 'model/logger/logger'
import { PrismaMongoClient } from '../database/prisma'

//TODO: 修改课程链接，链接是固定的
enum workLink {
  day1 = '1',
  day2 = '2',
  day3 = '3',
  day4 = '4',
  day5 = '5',
  day6 = '6',
}

/**
 * 对通用层注入函数
 */
export const override =  defineOverride({
  async onInit() {
    // 启动后钩子（可留空）
  },

  // 加好友后发送欢迎语
  async sendWelcomeMessage(chatId, userId) {
    const lock = new AsyncLock()
    await lock.acquire(chatId, async () => { // 如果有新消息，当前回复会被丢弃
      await chatStateStoreClient.update(chatId, {
        state: {
          is_friend_accepted: true
        }
      })

      const chat = await chatDBClient.getById(chatId)

      if (chat) {
        extractUserSlots.extractUserSlotsFromChatHistory({ chatId, chatHistory:[{
          role: 'user',
          date: dayjs().format('YYYY-MM-DD'),
          message: `我的名字是${chat.contact.wx_name}`
        }], topicRecommendations: extractUserSlots.getTopicRecommendations(), topicRules:extractUserSlots.getTopicRules() })
      }

      await startTasks(chatStateStoreClient, manifest.projectName, userId, chatId, calTaskTime)

      await commonMessageSender.sendText(chatId, {
        text: '😁您好呀，很开心认识您，我是您的《筹码主升训练营》的专属助教钱春艳。 \n👉接下来由我陪您学习，助您建立属于自己完整的交易体系! \n老师赠送的6节筹码课程在【8月17日（周天）】晚上19：20微信直播，记得置顶小钱微信 \n------------------- \n回复【666】免费领取筹码峰三大使用技巧 & 无延迟筹码工具',
        description: '欢迎语'
      })

    }, { timeout: 2 * 60 * 1000 })
  },

  async handleUnknownMessage(message) {

  },

  async handleImageMessage(imageUrl, chatId) {
    return `【图片Url】${imageUrl}`
  },

  //   async handleTextAndImageMessage(imageAndText: ImageAndText[], chatId: string, userId: string) {
  //     try {
  //       logger.debug('开始处理多模态消息', { chatId, userId, messageCount: imageAndText.length })
  //       // 过滤
  //       const validMessages = imageAndText.filter((item) => item.text || item.imageUrl)
  //
  //       if (validMessages.length === 0) {
  //         logger.warn('没有有效的消息内容', { chatId, userId })
  //         return '没有有效的消息内容'
  //       }
  //
  //       // 构建符合LangChain格式的多模态内容
  //       const multiModalContent: Array<{
  //         type: 'text' | 'image',
  //         text?: string,
  //         source_type?: 'url',
  //         url?: string
  //       }> = []
  //
  //       for (const item of validMessages) {
  //         if (item.text && item.text.trim().length > 0) {
  //           // 添加文本内容
  //           multiModalContent.push({
  //             type: 'text',
  //             text: item.text.trim()
  //           })
  //         }
  //
  //         if (item.imageUrl) {
  //           // 添加图片内容
  //           multiModalContent.push({
  //             type: 'image',
  //             source_type: 'url',
  //             url: item.imageUrl
  //           })
  //         }
  //       }
  //
  //       if (multiModalContent.length === 0) {
  //         logger.warn('没有有效的多模态内容', { chatId, userId })
  //         return '没有有效的多模态内容'
  //       }
  //
  //       // 构建专业的股票分析系统提示词
  //       const systemPrompt = `你是一名专业的股票分析师，擅长分析K线图、筹码分布图、技术指标图等各种股市技术图表。
  //
  // 用户会根据一张图片提出问题。如果用户提供的图片是股市图，请仔细分析图片中的股票技术信息，包括但不限于：
  // - K线形态和趋势分析（上涨、下跌、震荡等）
  // - 成交量变化情况
  // - 技术指标分析（如MACD、RSI、KDJ、布林带等）
  // - 筹码分布情况和密集区域
  // - 重要的支撑位和阻力位
  // - 均线系统分析
  // - 价格形态识别（如头肩顶、双底等）
  //
  // # 注意事项：
  // 1. 请根据图片内容和用户的问题，用一段话回答客户的问题。
  // 2. 请注意你是在用微信和客户聊天，要表现的更像真人打字输入，所以请不要用list或markdown之类的格式。
  // 3. 不需要追问多余的问题，只需要回答客户的问题即可`
  //
  //       // 使用GPT-5进行多模态分析
  //       const llm = new LLM({
  //         model: 'gpt-5',
  //         temperature: 0.3,
  //         maxTokens: 1500,
  //         meta: {
  //           promptName: 'haogu_stock_multimodal_analysis',
  //           chat_id: chatId,
  //           description: '股票多模态技术分析'
  //         }
  //       })
  //
  //       // 构建消息
  //       const humanMessage = new HumanMessage({
  //         content: multiModalContent
  //       })
  //
  //       const messages = [
  //         new SystemMessage(systemPrompt),
  //         humanMessage
  //       ]
  //
  //       logger.debug('调用GPT-5进行多模态分析', {
  //         chatId,
  //         userId
  //       })
  //
  //       const analysisResult = await llm.predictMessage(messages)
  //
  //       if (!analysisResult || analysisResult.trim().length === 0) {
  //         logger.error('GPT-5返回空结果', { chatId, userId })
  //         return 'GPT-5返回空结果'
  //       }
  //
  //       logger.log('GPT-5多模态分析完成', {
  //         chatId,
  //         userId,
  //         responseLength: analysisResult.length
  //       })
  //
  //       eventTrackClient.track(chatId, '股票多模态分析', {
  //         message_count: validMessages.length,
  //         has_image: multiModalContent.some((item) => item.type === 'image'),
  //         has_text: multiModalContent.some((item) => item.type === 'text'),
  //         response_length: analysisResult.length,
  //         model: 'gpt-5'
  //       })
  //
  //       logger.log('股票分析结果', { chatId, userId, result: analysisResult })
  //       return analysisResult
  //
  //     } catch (error) {
  //       logger.error('多模态股票分析失败', {
  //         chatId,
  //         userId
  //       })
  //       return '多模态股票分析失败'
  //     }
  //   },

  async handleVideoMessage(videoUrl, chatId) {
    return '【视频】'
  },

  // 完成作业
  async handleFinishWork(data: { chat_id: string } & ScrmWorkToAiStaff) {
    if (data.link == workLink.day1) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day1:true
        }
      })
    } else if (data.link == workLink.day2) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day2:true
        }
      })
    } else if (data.link == workLink.day3) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day3:true
        }
      })
    } else if (data.link == workLink.day4) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day4:true
        }
      })
    } else if (data.link == workLink.day5) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day5:true
        }
      })
    } else if (data.link == workLink.day6) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day6:true
        }
      })
    } else {
      logger.warn(`${data.chat_id}完成未知链接作业:${data.link}`)
    }

  },
  // 完成订单
  async handleOrder(data: {chat_id:string} & ScrmOrderPlacedToAiStaff) {
    await chatStateStoreClient.update(data.chat_id, {
      state:<IChattingFlag>{
        is_complete_payment:true
      }
    })
  },
  // 读消息
  async handleReadMessag(data:ScrmReadMarkToAiStaff) {
    const chatId = `${data.custUnifiedUserId}_${data.staffId}`
    const mongoClient = PrismaMongoClient.getInstance()
    await mongoClient.chat_history.updateMany({
      where:{
        chat_id:chatId
      },
      data:{
        is_read:true
      }
    })
  },
  //读链接
  async handleReadLink(data:ScrmLinkReadMarkToAiStaff) {

  },
  // 新客户
  async handleNewCustomer(data:ScrmCustomerEventToAiStaff) {
    if (data.status == 0 || data.status == 8 || data.status == 2049) {
      //添加
      const chatId = `${data.custUnifiedUserId}_${data.staffId}`
      const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
      if (flags.is_friend_accepted) {
        logger.log(`已经添加了好友,chat_id:${chatId}`)
        return
      }
      const mongoClient = PrismaMongoClient.getInstance()
      await mongoClient.chat.create({ data:{
        id:chatId,
        contact:{
          wx_id:data.custUnifiedUserId,
          wx_name:''
        },
        wx_id:String(data.staffId),
        created_at:new Date(),
        chat_state:{
          nodeInvokeCount:{},
          nextStage:'free_talk',
          userSlots:{},
          state:{}
        },
        course_no:Number(dayjs().format('YYYYMMDD')),
        conversation_id:data.conversationId,
        customer_unified_user_id:data.custUnifiedUserId,
        wx_union_id:data.custUnionId
      } })
      await this.sendWelcomeMessage(chatId, data.custUnifiedUserId)
    } else if (data.status == 9 || data.status == 2057 || data.status == 2313) {
      //删除
      const chatId = `${data.custUnifiedUserId}_${data.staffId}`
      const mongoClient = PrismaMongoClient.getInstance()
      await mongoClient.chat.update({ where:{
        id:chatId
      }, data:{
        is_deleted:true
      } })
    } else {
      logger.error('新客户接口未知status', data)
    }
  }
})
