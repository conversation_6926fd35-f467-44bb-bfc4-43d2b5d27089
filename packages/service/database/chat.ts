import { IChatState } from '../local_cache/type'
import { PrismaClient } from 'model/prisma_client'

export interface IChat {
  id: string
  round_ids: string[]
  contact: {
    wx_id: string
    wx_name: string
  }
  wx_id: string
  created_at: Date | null
  chat_state: IChatState
  moer_id?: string
  is_human_involved?:boolean
  course_no?: number
  is_stop_group_push?: boolean
  phone?: string
}

export class ChatDB {
  mongoClient:PrismaClient
  constructor(mongoClient:PrismaClient) {
    this.mongoClient = mongoClient
  }
  public async getChatByPhone(phone: string): Promise<IChat | null> {
    // @ts-ignore fuck you, primsa
    return this.mongoClient.chat.findFirst({
      where: {
        phone
      }
    })
  }

  public async removeById(chat_id: string) {
    return this.mongoClient.chat.delete({
      where: {
        id: chat_id
      }
    })
  }

  public async updateState(chat_id: string, chatState: IChatState) {
    return this.mongoClient.chat.update({
      where: {
        id: chat_id
      },
      data: {
        chat_state: chatState
      }
    })
  }

  public async create(chat: IChat): Promise<IChat> {
    // @ts-ignore fuck you, primsa
    return this.mongoClient.chat.create({
      data: chat
    })
  }

  public async pushRoundId(chatId: string, roundId: string) {
    if (!await this.getById(chatId)) {
      return
    }

    return this.mongoClient.chat.update({
      where: {
        id: chatId
      },
      data: {
        round_ids: {
          push: roundId
        }
      }
    })
  }

  public  async getById<T extends IChat>(id: string): Promise<T | null> {
    // @ts-ignore fuck you, primsa
    return this.mongoClient.chat.findUnique({
      where: {
        id
      }
    })
  }

  public  async deleteById(id: string) {
    return this.mongoClient.chat.delete({
      where: {
        id
      }
    })
  }

  public  async isHumanInvolvement(chatId: string): Promise<boolean> {
    const chat = await this.mongoClient.chat.findUnique({
      where: {
        id: chatId
      }
    })

    if (chat) {
      return Boolean(chat.is_human_involved)
    }

    return false
  }

  public async setHumanInvolvement(chatId: string, isHumanInvolved: boolean) {
    return this.mongoClient.chat.update({
      where: {
        id: chatId
      },
      data: {
        is_human_involved: isHumanInvolved
      }
    })
  }

  public async getCourseNo(chatId: string): Promise<number | null> {
    const chat = await this.mongoClient.chat.findUnique({
      where: {
        id: chatId
      }
    })

    if (chat) {
      return chat.course_no
    }

    return null
  }

  public async getPhone(chatId: string): Promise<string | null> {
    const chat = await this.mongoClient.chat.findUnique({
      where: {
        id: chatId
      }
    })

    if (chat) {
      return chat.phone
    }

    return null
  }


  async setStopGroupPush(chatId: string, stopPush: boolean) {
    return this.mongoClient.chat.update({
      where: {
        id: chatId
      },
      data: {
        is_stop_group_push: stopPush
      }
    })
  }

  async getChatStateById(chat_id: string) {
    const chat = await this.getById(chat_id)

    if (chat) {
      return chat.chat_state
    } else {
      return null
    }
  }

  async updateContact(chatId: string, userId: string, name: string) {
    return this.mongoClient.chat.update({
      where: {
        id: chatId
      },
      data: {
        contact: {
          wx_id: userId,
          wx_name: name
        }
      }
    })
  }
}